import React from 'react';

const TimeNode = ({ node, active, completed, onClick, index, total }) => {
  const getNodeStyle = () => {
    if (active) {
      return {
        container: 'transform scale-105',
        circle: 'w-6 h-6 bg-gradient-to-br from-red-500 to-red-600 border-2 border-red-400 shadow-md',
        card: 'bg-gradient-to-br from-red-500 to-red-600 text-white border-red-400 shadow-md',
        icon: 'text-white'
      };
    } else if (completed) {
      return {
        container: 'hover:transform hover:scale-105',
        circle: 'w-5 h-5 bg-gradient-to-br from-green-500 to-green-600 border border-green-400 shadow-sm',
        card: 'bg-white border-green-200 text-gray-800 hover:shadow-md',
        icon: 'text-white'
      };
    } else {
      return {
        container: 'hover:transform hover:scale-105',
        circle: 'w-5 h-5 bg-white border border-gray-300 shadow-sm hover:border-red-300',
        card: 'bg-white border-gray-200 text-gray-600 hover:border-red-200 hover:shadow-sm',
        icon: 'text-gray-400'
      };
    }
  };

  const styles = getNodeStyle();

  return (
    <div
      className={`flex flex-col items-center cursor-pointer transition-all duration-300 ${styles.container}`}
      onClick={onClick}
    >
      {/* 紧凑节点圆圈 */}
      <div className={`
        rounded-full transition-all duration-200 flex items-center justify-center relative
        ${styles.circle}
      `}>
        {/* 节点图标 */}
        {completed && !active ? (
          <svg className={`w-2.5 h-2.5 ${styles.icon}`} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        ) : active ? (
          <div className="w-2 h-2 bg-white rounded-full"></div>
        ) : (
          <span className="text-xs font-bold text-gray-500">{index + 1}</span>
        )}
      </div>

      {/* 紧凑事件卡片 */}
      <div className={`
        mt-2 px-2 py-1.5 rounded transition-all duration-200 border
        ${styles.card}
        min-w-[80px] max-w-[100px] text-center
      `}>
        <div className="text-xs font-medium leading-tight">
          {node.label}
        </div>
        <div className="text-xs opacity-75 mt-0.5">
          {node.date}
        </div>
      </div>

      {/* 连接线（除了最后一个节点） */}
      {index < total - 1 && (
        <div className={`
          absolute top-3 left-full w-full h-0.5 -z-10
          ${completed || active ? 'bg-gradient-to-r from-red-500 to-red-400' : 'bg-gray-200'}
          transition-all duration-500
        `}></div>
      )}
    </div>
  );
};

export default TimeNode;