import React, { useState } from 'react';

const EventFilter = ({ onFilterChange, isVisible, onToggle }) => {
  const [filters, setFilters] = useState({
    event_type: '',
    importance: '',
    category: '',
    search: ''
  });

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    const emptyFilters = {
      event_type: '',
      importance: '',
      category: '',
      search: ''
    };
    setFilters(emptyFilters);
    onFilterChange(emptyFilters);
  };

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed top-24 right-6 z-30 bg-white/90 backdrop-blur-sm hover:bg-white text-gray-700 hover:text-red-600 p-2.5 rounded-lg shadow-lg hover:shadow-xl border border-gray-200/50 transition-all duration-300 group"
      >
        <svg className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
        </svg>
      </button>
    );
  }

  return (
    <div className="fixed top-24 right-6 z-30 w-96 max-h-[80vh] overflow-hidden fade-in">
      <div className="bg-white/98 backdrop-blur-xl rounded-2xl border border-gray-200/80 shadow-2xl overflow-hidden">
        {/* 企业级头部 */}
        <div className="bg-gradient-to-r from-red-500 to-red-600 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 className="text-white font-bold text-lg">事件筛选</h3>
                <p className="text-red-100 text-sm">精确定位历史事件</p>
              </div>
            </div>
            <button
              onClick={onToggle}
              className="text-white/80 hover:text-white hover:bg-white/10 p-2 rounded-lg transition-all duration-200"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-6 space-y-6 max-h-[60vh] overflow-y-auto">

          {/* 企业级搜索框 */}
          <div className="space-y-3">
            <label className="block text-gray-700 text-sm font-semibold uppercase tracking-wide">
              智能搜索
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="搜索事件标题、描述、地点..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
              />
              {filters.search && (
                <button
                  onClick={() => handleFilterChange('search', '')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <svg className="w-4 h-4 text-gray-400 hover:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* 事件类型筛选 */}
          <div className="space-y-3">
            <label className="block text-gray-700 text-sm font-semibold uppercase tracking-wide">
              事件类型
            </label>
            <select
              value={filters.event_type}
              onChange={(e) => handleFilterChange('event_type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
            >
              <option value="">全部类型</option>
              <option value="military_command">军事指挥</option>
              <option value="major_battle">重大战役</option>
              <option value="victory_celebration">胜利庆祝</option>
              <option value="strategic_move">战略转移</option>
            </select>
          </div>

          {/* 重要性等级筛选 */}
          <div className="space-y-3">
            <label className="block text-gray-700 text-sm font-semibold uppercase tracking-wide">
              重要性等级
            </label>
            <select
              value={filters.importance}
              onChange={(e) => handleFilterChange('importance', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
            >
              <option value="">全部等级</option>
              <option value="critical">极其重要</option>
              <option value="high">高度重要</option>
              <option value="medium">中等重要</option>
              <option value="low">一般重要</option>
            </select>
          </div>

          {/* 事件分类筛选 */}
          <div className="space-y-3">
            <label className="block text-gray-700 text-sm font-semibold uppercase tracking-wide">
              事件分类
            </label>
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 bg-gray-50 hover:bg-white"
            >
              <option value="">全部分类</option>
              <option value="指挥部设立">指挥部设立</option>
              <option value="重大战役">重大战役</option>
              <option value="胜利庆祝">胜利庆祝</option>
              <option value="战略转移">战略转移</option>
            </select>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-3 pt-4 border-t border-gray-200">
            <button
              onClick={clearFilters}
              className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2.5 px-4 rounded-xl transition-all duration-200"
            >
              清除筛选
            </button>
            <button
              onClick={onToggle}
              className="flex-1 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium py-2.5 px-4 rounded-xl transition-all duration-200"
            >
              应用筛选
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventFilter;
