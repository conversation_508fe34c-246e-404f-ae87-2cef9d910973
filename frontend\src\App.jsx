import React, { useState, useEffect } from 'react';
import MapContainer from './components/Map/MapContainer';
import Timeline from './components/Timeline/Timeline';
import Header from './components/Common/Header';
import Loading from './components/Common/Loading';
import EventFilter from './components/Common/EventFilter';
import ErrorBoundary from './components/Common/ErrorBoundary';

import { fetchEvents, fetchTimeline } from './services/api';

function App() {
  const [currentTimeIndex, setCurrentTimeIndex] = useState(7); // 初始显示所有事件
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [events, setEvents] = useState([]);
  const [timeline, setTimeline] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({});
  const [showFilter, setShowFilter] = useState(false);


  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        console.log('正在从后端获取数据，筛选条件:', filters);

        // 并行加载事件和时间线数据
        const [eventsData, timelineData] = await Promise.all([
          fetchEvents(filters),
          fetchTimeline()
        ]);

        console.log('后端返回事件数据:', eventsData);
        console.log('后端返回时间线数据:', timelineData);

        if (eventsData && eventsData.events) {
          setEvents(eventsData.events);
          console.log('成功加载', eventsData.events.length, '个事件');
        } else {
          console.warn('后端返回事件数据格式异常:', eventsData);
          setEvents([]);
        }

        if (timelineData && timelineData.timeline) {
          setTimeline(timelineData.timeline);
          console.log('成功加载', timelineData.timeline.length, '个时间线节点');
        } else {
          console.warn('后端返回时间线数据格式异常:', timelineData);
          setTimeline([]);
        }

        setLoading(false);
      } catch (err) {
        console.error('后端连接失败:', err);

        // 显示错误提示但不使用模拟数据
        alert('无法连接到后端服务器，请确保后端服务正在运行在 http://localhost:8000');
        setEvents([]);
        setTimeline([]);
        setLoading(false);
      }
    };

    loadData();
  }, [filters]);

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  if (loading) return <Loading />;

  return (
    <ErrorBoundary>
      <div className="w-screen h-screen relative overflow-hidden bg-gray-50">

      {/* 头部组件 - 紧凑版 */}
      <Header />

      {/* 事件筛选组件 */}
      <EventFilter
        onFilterChange={handleFilterChange}
        isVisible={showFilter}
        onToggle={() => setShowFilter(!showFilter)}
      />

      {/* 地图容器 - 优化布局，充分利用空间 */}
      <div className="absolute top-14 bottom-20 left-0 right-0">
        <MapContainer
          events={events}
          selectedEvent={selectedEvent}
          onEventSelect={setSelectedEvent}
          currentTimeIndex={currentTimeIndex}
        />
      </div>

      {/* 时间线组件 - 紧凑版 */}
      <Timeline
        timeline={timeline}
        currentIndex={currentTimeIndex}
        onIndexChange={setCurrentTimeIndex}
      />

      </div>
    </ErrorBoundary>
  );
}

export default App;