import React, { useState, useEffect, useRef } from 'react';
import AIChat from '../AI/AIChat';

const EventPopup = ({ event, mapInstanceRef, onClose }) => {
  const [showAIChat, setShowAIChat] = useState(false);
  const popupRef = useRef(null);

  useEffect(() => {
    if (!event || !mapInstanceRef.current || !popupRef.current) return;

    const map = mapInstanceRef.current;
    const popup = popupRef.current;

    // 简单直接的定位方式：获取地图中心点，在右侧显示弹窗
    const mapContainer = map.getContainer();
    const mapRect = mapContainer.getBoundingClientRect();

    // 弹窗显示在地图右侧中央
    const popupX = mapRect.width - 420; // 距离右边缘20px，弹窗宽度400px
    const popupY = mapRect.height / 2;

    popup.style.left = `${Math.max(20, popupX)}px`;
    popup.style.top = `${popupY}px`;
    popup.style.transform = 'translateY(-50%)';
  }, [event, mapInstanceRef]);

  if (!event) return null;

  return (
    <>
      {/* 企业级点位弹窗 */}
      <div
        ref={popupRef}
        className="absolute z-[60] w-96 transition-all duration-300 scale-in"
      >
        {/* 主卡片 */}
        <div className="bg-white/98 backdrop-blur-xl rounded-2xl border border-gray-200/80 shadow-2xl overflow-hidden">
          {/* 顶部装饰条 */}
          <div className="h-1 bg-gradient-to-r from-red-500 to-red-600"></div>

          {/* 头部区域 */}
          <div className="bg-gradient-to-br from-gray-50 to-white p-4 border-b border-gray-100">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 flex-1">
                <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg flex-shrink-0">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-bold text-gray-900 leading-tight mb-1">
                    {event.title}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <span className="inline-flex items-center px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                      {event.date}
                    </span>
                    {event.category && (
                      <span className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                        {event.category}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <button
                onClick={onClose}
                className="ml-2 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="p-4 space-y-4">
            {/* 描述 */}
            <p className="text-gray-700 text-sm leading-relaxed text-truncate-3">
              {event.description}
            </p>

            {/* 详细信息 */}
            {(event.location || event.participants) && (
              <div className="space-y-2">
                {event.location && (
                  <div className="flex items-center space-x-2 text-sm">
                    <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-600">{event.location}</span>
                  </div>
                )}
                {event.participants && (
                  <div className="flex items-start space-x-2 text-sm">
                    <svg className="w-4 h-4 text-gray-400 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                    </svg>
                    <div className="flex flex-wrap gap-1">
                      {event.participants.slice(0, 3).map((person, index) => (
                        <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                          {person}
                        </span>
                      ))}
                      {event.participants.length > 3 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-500 rounded text-xs">
                          +{event.participants.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex space-x-2 pt-2">
              <button
                onClick={() => setShowAIChat(true)}
                className="flex-1 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-sm font-semibold py-2.5 px-4 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                </svg>
                <span>AI解读</span>
              </button>
              <button
                onClick={onClose}
                className="px-4 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-xl transition-all duration-200"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* AI对话弹窗 - 独立的全屏弹窗 */}
      {showAIChat && (
        <AIChat
          event={event}
          onClose={() => setShowAIChat(false)}
        />
      )}
    </div>
  );
};

export default EventPopup;