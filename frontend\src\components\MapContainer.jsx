import React, { useEffect, useRef, useState } from 'react';
import EventMarker from './Map/EventMarker';
import EventPopup from './Map/EventPopup';

const MapContainer = ({ events = [], selectedEvent, onEventSelect, currentTimeIndex }) => {
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const [layerType, setLayerType] = useState('normal'); // 'normal' or 'satellite'

  useEffect(() => {
    if (window.AMap && mapRef.current) {
      // 初始化地图（默认底图）
      const map = new window.AMap.Map(mapRef.current, {
        center: [113.2047, 36.8373],
        zoom: 11,
        mapStyle: 'amap://styles/whitesmoke',
        showLabel: true,
      });
      mapInstanceRef.current = map;

      // 行政区划高亮
      window.AMap.plugin('AMap.DistrictSearch', function () {
        const district = new window.AMap.DistrictSearch({
          level: 'district',
          showbiz: false
        });
        district.search('武乡县', (status, result) => {
          if (status === 'complete' && result.districtList && result.districtList[0].boundaries) {
            const bounds = result.districtList[0].boundaries;
            bounds.forEach(boundary => {
              const polygon = new window.AMap.Polygon({
                path: boundary,
                strokeColor: '#FF6B6B',
                strokeWeight: 3,
                strokeOpacity: 0.8,
                fillColor: '#FF6B6B',
                fillOpacity: 0.1,
                strokeStyle: 'dashed'
              });
              map.add(polygon);
            });
          }
        });
      });

      // 卫星图层（默认不显示）
      const satelliteLayer = new window.AMap.TileLayer.Satellite({zIndex: 10});
      mapInstanceRef.current.satelliteLayer = satelliteLayer;

      // 添加历史事件点位
      mockEvents.forEach(event => {
        const marker = new window.AMap.Marker({
          position: event.coordinate,
          title: event.title,
        });
        marker.setMap(map);
        marker.on('click', () => {
          window.AMap.InfoWindow && new window.AMap.InfoWindow({
            content: `<div style='min-width:180px'><b>${event.title}</b><br/>${event.description}</div>`,
            offset: new window.AMap.Pixel(0, -30)
          }).open(map, event.coordinate);
        });
      });
    }
  }, []);

  // 切换底图/卫星图
  const handleLayerSwitch = (type) => {
    if (!mapInstanceRef.current) return;
    setLayerType(type);
    if (type === 'normal') {
      mapInstanceRef.current.remove(mapInstanceRef.current.satelliteLayer);
    } else {
      mapInstanceRef.current.add(mapInstanceRef.current.satelliteLayer);
    }
  };

  return (
    <div style={{ width: '100vw', height: '90vh', position: 'relative' }}>
      <div ref={mapRef} style={{ width: '100%', height: '100%' }} />
      {/* 图层切换按钮 */}
      <div style={{ position: 'absolute', top: 20, right: 20, zIndex: 1000 }}>
        <button
          onClick={() => handleLayerSwitch('normal')}
          style={{
            marginRight: 8,
            padding: '6px 16px',
            background: layerType === 'normal' ? '#FF6B6B' : '#fff',
            color: layerType === 'normal' ? '#fff' : '#333',
            border: '1px solid #FF6B6B',
            borderRadius: 4,
            cursor: 'pointer',
            fontWeight: 'bold'
          }}
        >标准图</button>
        <button
          onClick={() => handleLayerSwitch('satellite')}
          style={{
            padding: '6px 16px',
            background: layerType === 'satellite' ? '#FF6B6B' : '#fff',
            color: layerType === 'satellite' ? '#fff' : '#333',
            border: '1px solid #FF6B6B',
            borderRadius: 4,
            cursor: 'pointer',
            fontWeight: 'bold'
          }}
        >卫星图</button>
      </div>

      {/* 渲染事件标记 */}
      {events.map(event => (
        <EventMarker
          key={event.id}
          event={event}
          mapInstanceRef={mapInstanceRef}
          isVisible={true}
          onClick={onEventSelect}
        />
      ))}

      {/* 渲染事件弹窗 */}
      {selectedEvent && (
        <EventPopup
          event={selectedEvent}
          mapInstanceRef={mapInstanceRef}
          onClose={() => onEventSelect(null)}
        />
      )}
    </div>
  );
};

export default MapContainer;