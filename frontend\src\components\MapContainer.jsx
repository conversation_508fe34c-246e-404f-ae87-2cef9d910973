import React, { useEffect, useRef, useState } from 'react';
import EventMarker from './Map/EventMarker';
import EventPopup from './Map/EventPopup';

const MapContainer = ({ events = [], selectedEvent, onEventSelect, currentTimeIndex }) => {
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const [layerType, setLayerType] = useState('normal'); // 'normal' or 'satellite'

  useEffect(() => {
    if (window.AMap && mapRef.current) {
      // 初始化地图（默认底图）
      const map = new window.AMap.Map(mapRef.current, {
        center: [113.2047, 36.8373],
        zoom: 11,
        mapStyle: 'amap://styles/whitesmoke',
        showLabel: true,
      });
      mapInstanceRef.current = map;

      // 行政区划高亮
      window.AMap.plugin('AMap.DistrictSearch', function () {
        const district = new window.AMap.DistrictSearch({
          level: 'district',
          showbiz: false
        });
        district.search('武乡县', (status, result) => {
          if (status === 'complete' && result.districtList && result.districtList[0].boundaries) {
            const bounds = result.districtList[0].boundaries;
            bounds.forEach(boundary => {
              const polygon = new window.AMap.Polygon({
                path: boundary,
                strokeColor: '#FF6B6B',
                strokeWeight: 3,
                strokeOpacity: 0.8,
                fillColor: '#FF6B6B',
                fillOpacity: 0.1,
                strokeStyle: 'dashed'
              });
              map.add(polygon);
            });
          }
        });
      });

      // 卫星图层（默认不显示）
      const satelliteLayer = new window.AMap.TileLayer.Satellite({zIndex: 10});
      mapInstanceRef.current.satelliteLayer = satelliteLayer;

      // 不再添加原生marker，使用EventMarker组件代替
    }
  }, []);

  // 切换底图/卫星图
  const handleLayerSwitch = (type) => {
    if (!mapInstanceRef.current || !mapInstanceRef.current.satelliteLayer) return;

    setLayerType(type);

    if (type === 'normal') {
      // 移除卫星图层
      mapInstanceRef.current.remove(mapInstanceRef.current.satelliteLayer);
    } else if (type === 'satellite') {
      // 添加卫星图层
      mapInstanceRef.current.add(mapInstanceRef.current.satelliteLayer);
    }
  };

  return (
    <div style={{ width: '100vw', height: '90vh', position: 'relative' }}>
      <div ref={mapRef} style={{ width: '100%', height: '100%' }} />
      {/* 图层切换按钮 - 精致小巧版本 */}
      <div className="absolute top-4 right-4 z-[1000] flex bg-white/90 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200/50 overflow-hidden">
        <button
          onClick={() => handleLayerSwitch('normal')}
          className={`px-3 py-1.5 text-xs font-medium transition-all duration-200 ${
            layerType === 'normal'
              ? 'bg-red-500 text-white'
              : 'text-gray-600 hover:bg-gray-50'
          }`}
        >
          地形
        </button>
        <div className="w-px bg-gray-200"></div>
        <button
          onClick={() => handleLayerSwitch('satellite')}
          className={`px-3 py-1.5 text-xs font-medium transition-all duration-200 ${
            layerType === 'satellite'
              ? 'bg-red-500 text-white'
              : 'text-gray-600 hover:bg-gray-50'
          }`}
        >
          卫星
        </button>
      </div>

      {/* 渲染事件标记 */}
      {events.map(event => (
        <EventMarker
          key={event.id}
          event={event}
          mapInstanceRef={mapInstanceRef}
          isVisible={true}
          onClick={onEventSelect}
        />
      ))}

      {/* 渲染事件弹窗 */}
      {selectedEvent && (
        <EventPopup
          event={selectedEvent}
          mapInstanceRef={mapInstanceRef}
          onClose={() => onEventSelect(null)}
        />
      )}
    </div>
  );
};

export default MapContainer;