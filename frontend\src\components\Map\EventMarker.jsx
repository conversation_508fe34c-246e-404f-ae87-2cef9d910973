import { useEffect, useRef } from 'react';

const EventMarker = ({ event, mapInstanceRef, isVisible, onClick }) => {
  const markerRef = useRef(null);

  useEffect(() => {
    if (!mapInstanceRef.current || !event || !isVisible) return;

    // 清理之前的标记
    if (markerRef.current) {
      markerRef.current.setMap(null);
      markerRef.current = null;
    }

    // 创建简单的红色圆形标记图标
    const createMarkerIcon = (size = 16, color = '#D32F2F') => {
      return `data:image/svg+xml;base64,${btoa(`
        <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
          <circle cx="${size/2}" cy="${size/2}" r="${size/2 - 1}"
                  fill="${color}"
                  stroke="#FFFFFF"
                  stroke-width="2"
                  style="filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3))"/>
          <circle cx="${size/2}" cy="${size/2}" r="${size/4}"
                  fill="#FFFFFF"
                  opacity="0.9"/>
        </svg>
      `)}`;
    };

    // 为重叠的标记添加偏移
    const getMarkerOffset = (eventId, coordinate) => {
      // 砖壁村位置的事件 (id: 3, 6, 7)
      if (coordinate[0] === 113.2462 && coordinate[1] === 36.8636) {
        if (eventId === 3) return new window.AMap.Pixel(-8, -8); // 中心
        if (eventId === 6) return new window.AMap.Pixel(-18, -18); // 左上
        if (eventId === 7) return new window.AMap.Pixel(2, -18); // 右上
      }
      // 王家峪位置的事件 (id: 5, 8)
      if (coordinate[0] === 113.2102 && coordinate[1] === 36.8882) {
        if (eventId === 5) return new window.AMap.Pixel(-8, -8); // 中心
        if (eventId === 8) return new window.AMap.Pixel(-18, 2); // 左下
      }
      return new window.AMap.Pixel(-8, -8); // 默认偏移
    };

    // 创建标记
    const marker = new window.AMap.Marker({
      position: event.coordinate,
      title: event.title,
      offset: getMarkerOffset(event.id, event.coordinate),
      icon: createMarkerIcon(16),
      zIndex: 100,
      extData: event
    });

    // 添加点击事件
    marker.on('click', (e) => {
      console.log('标记被点击:', event.title, event);
      e.stopPropagation(); // 阻止事件冒泡
      if (onClick) {
        onClick(event);
      }
    });

    // 添加悬停效果
    marker.on('mouseover', () => {
      marker.setIcon(createMarkerIcon(20, '#FF5722'));
    });

    marker.on('mouseout', () => {
      marker.setIcon(createMarkerIcon(16, '#D32F2F'));
    });

    // 添加到地图
    mapInstanceRef.current.add(marker);
    markerRef.current = marker;

    return () => {
      if (markerRef.current) {
        markerRef.current.setMap(null);
        markerRef.current = null;
      }
    };
  }, [event, mapInstanceRef, isVisible, onClick]);

  return null;
};

export default EventMarker;