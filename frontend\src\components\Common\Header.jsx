import React from 'react';

const Header = () => {
  return (
    <header className="absolute top-0 left-0 right-0 z-40 bg-white/95 backdrop-blur-sm border-b border-gray-200/60 shadow-md">
      <div className="max-w-7xl mx-auto px-4 py-2">
        <div className="flex items-center justify-between">
          {/* 左侧品牌区域 - 紧凑版 */}
          <div className="flex items-center space-x-3">
            {/* 品牌图标 - 缩小版 */}
            <div
              className="relative w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center cursor-pointer hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-md group"
              onDoubleClick={() => window.open('/admin', '_blank')}
              title="双击进入管理界面"
            >
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
              </svg>
              <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-green-500 rounded-full border border-white"></div>
            </div>

            {/* 品牌信息 - 单行版 */}
            <div>
              <h1 className="text-lg font-bold text-gray-900">
                武乡革命红色地图
              </h1>
            </div>
          </div>

          {/* 右侧信息面板 - 紧凑版 */}
          <div className="flex items-center space-x-3">
            {/* 历史时期卡片 - 紧凑版 */}
            <div className="bg-red-50 border border-red-200 rounded-lg px-3 py-1.5">
              <div className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                <span className="text-sm font-medium text-red-800">1937-1942</span>
              </div>
            </div>

            {/* 事件统计卡片 - 紧凑版 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg px-3 py-1.5">
              <div className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-blue-800">8个事件</span>
              </div>
            </div>

            {/* 状态指示器 - 紧凑版 */}
            <div className="flex items-center space-x-1.5 bg-green-50 border border-green-200 rounded-lg px-2 py-1.5">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs font-medium text-green-700">正常</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
