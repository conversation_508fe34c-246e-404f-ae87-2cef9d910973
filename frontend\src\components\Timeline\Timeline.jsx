import React, { useState } from 'react';
import TimeNode from './TimeNode';

const Timeline = ({ timeline, currentIndex, onIndexChange }) => {
  if (!timeline || timeline.length === 0) {
    return null;
  }

  const progress = ((currentIndex + 1) / timeline.length) * 100;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 fade-in">
      {/* 紧凑时间线容器 */}
      <div className="bg-white/95 backdrop-blur-sm border-t border-gray-200/60 shadow-lg">
        {/* 顶部装饰条 - 更细 */}
        <div className="h-0.5 bg-gradient-to-r from-red-500 via-red-600 to-red-500"></div>

        {/* 主要内容区域 - 紧凑版 */}
        <div className="max-w-7xl mx-auto px-4 py-3">
          {/* 时间节点网格 - 紧凑版 */}
          <div className="grid grid-cols-4 md:grid-cols-8 gap-2 mb-3">
            {timeline.map((node, idx) => (
              <TimeNode
                key={node.id}
                node={node}
                active={idx === currentIndex}
                completed={idx < currentIndex}
                onClick={() => onIndexChange(idx)}
                index={idx}
                total={timeline.length}
              />
            ))}
          </div>

          {/* 当前事件详情卡片 - 紧凑版 */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">{currentIndex + 1}</span>
                </div>
                <div>
                  <h3 className="text-base font-bold text-gray-900">
                    {timeline[currentIndex]?.label}
                  </h3>
                  <p className="text-xs text-gray-600">
                    {timeline[currentIndex]?.description}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                {/* 日期显示 - 紧凑版 */}
                <div className="inline-flex items-center px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-medium">
                  {timeline[currentIndex]?.date}
                </div>

                {/* 导航按钮 - 紧凑版 */}
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => currentIndex > 0 && onIndexChange(currentIndex - 1)}
                    disabled={currentIndex === 0}
                    className="p-1.5 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                  <button
                    onClick={() => currentIndex < timeline.length - 1 && onIndexChange(currentIndex + 1)}
                    disabled={currentIndex === timeline.length - 1}
                    className="p-1.5 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Timeline;